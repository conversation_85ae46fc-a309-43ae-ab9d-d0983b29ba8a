#!/usr/bin/env python3
"""
Point Detection Evaluation Script

This script evaluates point detection results by:
1. Loading inference results (Point geometries in UTM coordinates)
2. Loading ground truth (bbox/polygon geometries, using their centers)
3. Reprojecting ground truth to match inference CRS (UTM)
4. Using Hungarian algorithm to match points
5. Calculating mean distance and counting missed points
6. Discarding point pairs with distance > 0.15m (15cm)

Usage:
    python evaluate.py <inference_shp> <ground_truth_shp>
"""

import argparse
import sys
import warnings
from pathlib import Path
from typing import Any, Dict, Tuple
import pandas as pd
import geopandas as gpd
import numpy as np
from scipy.optimize import linear_sum_assignment
from scipy.spatial import distance_matrix
from shapely.geometry import LineString

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

target_crs = "EPSG:32647"


def load_inference_points(inference_shp_path: str) -> gpd.GeoSeries:
    """
    Load inference results from shapefile.

    Args:
        inference_shp_path: Path to inference shapefile containing Point geometries

    Returns:
        GeoDataFrame with Point geometries in UTM coordinates
    """
    print(f"Loading inference results from: {inference_shp_path}")

    # Load inference shapefile
    inference_gdf = gpd.read_file(inference_shp_path).to_crs(target_crs)
    inference_gdf = inference_gdf[inference_gdf.geometry.geom_type.eq("Point")]

    print(f"Loaded {len(inference_gdf)} inference points")
    print(f"Inference CRS: {inference_gdf.crs}")

    # Verify all geometries are Points
    non_points = inference_gdf[~inference_gdf.geometry.geom_type.eq("Point")]
    if len(non_points) > 0:
        print(f"Warning: Found {len(non_points)} non-Point geometries in inference data")
        inference_gdf = inference_gdf[inference_gdf.geometry.geom_type.eq("Point")]
        print(f"Filtered to {len(inference_gdf)} Point geometries")

    return inference_gdf.geometry


def load_ground_truth_points(gt_path: str, target_crs: Any) -> gpd.GeoSeries:
    """
    Load ground truth from shapefile and convert to center points.

    Args:
        gt_path: Path to ground truth shapefile containing bbox/polygon geometries
        target_crs: Target CRS to reproject to (should match inference CRS)

    Returns:
        GeoDataFrame with Point geometries representing centers of ground truth bboxes
    """
    print(f"Loading ground truth from: {gt_path}")

    # Load ground truth shapefile
    gt_gdf = gpd.read_file(gt_path).to_crs(target_crs)
    gt_gdf = gt_gdf[gt_gdf.geometry.geom_type.eq("Point")]

    print(f"Loaded {len(gt_gdf)} ground truth geometries")
    print(f"Ground truth CRS: {gt_gdf.crs}")

    # Convert geometries to center points
    print("Converting ground truth geometries to center points...")
    gt_points_gdf = gt_gdf.centroid

    print(f"Created {len(gt_points_gdf)} ground truth center points")
    return gt_points_gdf


def calculate_distance_matrix(inference_points: gpd.GeoSeries, gt_points: gpd.GeoSeries) -> np.ndarray:
    """
    Calculate distance matrix between inference points and ground truth points.

    Args:
        inference_points: GeoSeries with inference Point geometries
        gt_points: GeoSeries with ground truth Point geometries

    Returns:
        Distance matrix of shape (n_inference, n_gt) in meters
    """
    print("Calculating distance matrix...")

    # Extract coordinates
    inf_coords = np.array([inference_points.x, inference_points.y]).T
    gt_coords = np.array([gt_points.x, gt_points.y]).T
    distances = distance_matrix(inf_coords, gt_coords)

    print(f"Distance matrix shape: {distances.shape}")
    print(f"Min distance: {distances.min():.3f}m, Max distance: {distances.max():.3f}m")

    return distances


def greedy_match(cost_matrix: np.ndarray, max_distance: float):
    """
    对给定的代价矩阵执行贪婪最小距离匹配。

    参数
    ----
    cost_matrix : np.ndarray, shape (m, n)
        代价矩阵，其中 cost_matrix[i, j] 表示第 i 个点与第 j 个点之间的代价（距离）。

    返回
    ----
    matches : List[Tuple[int, int]]
        匹配结果列表，每个元组 (i, j) 表示第 i 行与第 j 列的点被匹配。
    """
    m, n = cost_matrix.shape
    # 生成 (i, j, distance) 三元组列表
    all_pairs = [(i, j, cost_matrix[i, j]) for i in range(m) for j in range(n) if cost_matrix[i, j] <= max_distance]
    # 按距离从小到大排序
    all_pairs.sort(key=lambda x: x[2])

    matched_rows = set()
    matched_cols = set()
    matches = []

    # 依次挑选最小距离的可用配对
    for i, j, dist in all_pairs:
        if i not in matched_rows and j not in matched_cols:
            matched_rows.add(i)
            matched_cols.add(j)
            matches.append((i, j))
            # 当一方所有点都已匹配，可提前结束
            if len(matched_rows) == m or len(matched_cols) == n:
                break

    matched_indices = np.array(matches)
    inf_indices = matched_indices[:, 0]
    gt_indices = matched_indices[:, 1]
    matched_distances = cost_matrix[inf_indices, gt_indices]

    return inf_indices, gt_indices, matched_distances


def create_matching_lines(
    inference_points: gpd.GeoSeries,
    gt_points: gpd.GeoSeries,
    matched_inf_idx: np.ndarray,
    matched_gt_idx: np.ndarray,
    matched_distances: np.ndarray,
    output_path,
    target_crs="EPSG:32647",
) -> None:
    """
    Create polylines between matched inference and ground truth points.

    Args:
        inference_points: GeoSeries with inference Point geometries
        gt_points: GeoSeries with ground truth Point geometries
        matched_inf_idx: Indices of matched inference points
        matched_gt_idx: Indices of matched ground truth points
        matched_distances: Distances of matched pairs
        output_path: Path to save the polyline shapefile
        target_crs: Target CRS for the output shapefile
    """
    print(f"Creating polylines between {len(matched_inf_idx)} matched pairs...")

    if len(matched_inf_idx) == 0:
        print("No matched pairs found, skipping polyline creation.")
        return

    # Create LineString geometries for each matched pair
    lines = []
    line_data = []

    for i, (inf_idx, gt_idx) in enumerate(zip(matched_inf_idx, matched_gt_idx)):
        # Get the matched points
        inf_point = inference_points.iloc[inf_idx]
        gt_point = gt_points.iloc[gt_idx]

        # Create LineString connecting the two points
        line = LineString([(inf_point.x, inf_point.y), (gt_point.x, gt_point.y)])

        lines.append(line)
        line_data.append(
            {
                "pair_id": i + 1,
                "inf_x": inf_point.x,
                "inf_y": inf_point.y,
                "gt_x": gt_point.x,
                "gt_y": gt_point.y,
                "inf_idx": inf_idx,
                "gt_idx": gt_idx,
            }
        )

    # Create GeoDataFrame with the lines
    lines_gdf = gpd.GeoDataFrame(line_data, geometry=lines, crs=target_crs)

    # Save to shapefile
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    lines_gdf.to_file(output_path, encoding="utf-8", driver="Arrow")

    print(f"Saved {len(lines_gdf)} polylines to: {output_path}")
    # print("Polyline attributes:")
    # print("  - pair_id: Sequential ID for each matched pair")
    # print("  - distance: Distance between matched points (meters)")
    # print("  - inf_x, inf_y: Inference point coordinates")
    # print("  - gt_x, gt_y: Ground truth point coordinates")
    # print("  - inf_idx, gt_idx: Original indices in the input shapefiles")


def evaluate_detection(
    inference_path: Path, gt_shp_path: Path, max_distance: float, line_output_path: Path
) -> Dict[str, Any]:
    """
    Evaluate point detection performance.

    Args:
        inference_shp_path: Path to inference shapefile
        gt_shp_path: Path to ground truth shapefile
        max_distance: Maximum distance threshold for valid matches (meters)
        line_output_path: Optional path to save polylines between matched pairs

    Returns:
        Dictionary containing evaluation metrics
    """
    print("=" * 60)
    print("POINT DETECTION EVALUATION")
    print("=" * 60)

    # Load inference points
    inference_gdf = load_inference_points(inference_path)

    # Load ground truth and convert to points
    gt_gdf = load_ground_truth_points(gt_shp_path, inference_gdf.crs)

    # Check if we have data to evaluate
    if len(inference_gdf) == 0:
        print("Error: No inference points found!")
        return {"error": "No inference points"}

    if len(gt_gdf) == 0:
        print("Error: No ground truth points found!")
        return {"error": "No ground truth points"}

    print("\nEvaluation setup:")
    print(f"  Inference points: {len(inference_gdf)}")
    print(f"  Ground truth points: {len(gt_gdf)}")
    print(f"  Max distance threshold: {max_distance}m\n")

    # Calculate distance matrix
    distance_matrix = calculate_distance_matrix(inference_gdf, gt_gdf)

    # Perform Hungarian matching
    # inf_indices, gt_indices, matched_distances = hungarian_matching(distance_matrix, max_distance)
    inf_indices, gt_indices, matched_distances = greedy_match(distance_matrix, max_distance)

    # Calculate metrics
    n_inference = len(inference_gdf)
    n_ground_truth = len(gt_gdf)
    n_matched = len(matched_distances)
    n_missed_gt = n_ground_truth - n_matched
    n_false_positives = n_inference - n_matched

    # Calculate mean distance for valid matches
    mean_distance = np.mean(matched_distances) if n_matched > 0 else 0.0

    # Calculate precision, recall, F1
    precision = n_matched / n_inference if n_inference > 0 else 0.0
    recall = n_matched / n_ground_truth if n_ground_truth > 0 else 0.0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

    # Create polylines if requested
    if line_output_path and n_matched > 0:
        print("\nCreating polylines between matched pairs...")
        create_matching_lines(
            inference_gdf, gt_gdf, inf_indices, gt_indices, matched_distances, line_output_path, target_crs
        )

    # Prepare results
    results = {
        "name": inference_path.name,
        "n_inference": n_inference,
        "n_ground_truth": n_ground_truth,
        "n_matched": n_matched,
        "n_missed_gt": n_missed_gt,
        "n_false_positives": n_false_positives,
        "mean_distance": round(mean_distance, 3),
        "max_distance_threshold": max_distance,
        "precision": round(precision, 4),
        "recall": round(recall, 4),
        "f1_score": round(f1_score, 4),
        "matched_distances": matched_distances.tolist() if n_matched > 0 else [],
    }

    return results


def print_evaluation_results(results: Dict[str, Any]) -> None:
    """
    Print evaluation results in a formatted way.

    Args:
        results: Dictionary containing evaluation metrics
    """
    if "error" in results:
        print(f"Evaluation failed: {results['error']}")
        return

    print("\n" + "=" * 60)
    print("EVALUATION RESULTS")
    print("=" * 60)

    print(f"Dataset Statistics:")
    print(f"  Inference points:     {results['n_inference']:6d}")
    print(f"  Ground truth points:  {results['n_ground_truth']:6d}")
    print()

    print(f"Matching Results (max distance: {results['max_distance_threshold']}m):")
    print(f"  Matched pairs:        {results['n_matched']:6d}")
    print(f"  Missed GT points:     {results['n_missed_gt']:6d}")
    print(f"  False positives:      {results['n_false_positives']:6d}")
    print()

    print(f"Distance Statistics:")
    if results["n_matched"] > 0:
        print(f"  Mean distance:        {results['mean_distance']:6.3f}m")
        distances = np.array(results["matched_distances"])
        print(f"  Min distance:         {distances.min():6.3f}m")
        print(f"  Max distance:         {distances.max():6.3f}m")
        print(f"  Std distance:         {distances.std():6.3f}m")
    else:
        print(f"  No valid matches found")
    print()

    print(f"Performance Metrics:")
    print(f"  Precision:            {results['precision']:6.3f} ({results['precision'] * 100:5.1f}%)")
    print(f"  Recall:               {results['recall']:6.3f} ({results['recall'] * 100:5.1f}%)")
    print(f"  F1-Score:             {results['f1_score']:6.3f} ({results['f1_score'] * 100:5.1f}%)")
    print()


def collect_reults(results, output_path: Path):
    df = pd.DataFrame(results)
    df = df.sort_values(by="name")
    df.to_csv(output_path, index=False)


def main():
    """Main function to run the evaluation."""
    parser = argparse.ArgumentParser(description="Evaluate point detection results using Hungarian algorithm matching")
    parser.add_argument("inference_shp", help="Path to inference shapefile (Point geometries)")
    parser.add_argument("ground_truth_shp", help="Path to ground truth shapefile (bbox/polygon geometries)")
    parser.add_argument(
        "--max-distance",
        type=float,
        default=0.3,
        help="Maximum distance threshold for valid matches in meters (default: 0.15)",
    )
    parser.add_argument("--line", type=str, help="Path to save polylines between matched pairs as shapefile")

    args = parser.parse_args()

    # Check if files exist
    if not Path(args.inference_shp).exists():
        print(f"Error: Inference shapefile not found: {args.inference_shp}")
        sys.exit(1)

    if not Path(args.ground_truth_shp).exists():
        print(f"Error: Ground truth shapefile not found: {args.ground_truth_shp}")
        sys.exit(1)

    # Run evaluation
    results = evaluate_detection(args.inference_shp, args.ground_truth_shp, args.max_distance, args.line)

    # Print results
    print_evaluation_results(results)


def main_batch():
    """Main function to run the evaluation."""
    parser = argparse.ArgumentParser(description="Evaluate point detection results using Hungarian algorithm matching")
    parser.add_argument("inference_dir", help="Path to inference shapefile (Point geometries)")
    parser.add_argument("ground_truth_dir", help="Path to ground truth shapefile (bbox/polygon geometries)")
    parser.add_argument("line_dir", help="Path to output directory")
    parser.add_argument(
        "--max-distance",
        type=float,
        default=0.3,
        help="Maximum distance threshold for valid matches in meters (default: 0.15)",
    )

    args = parser.parse_args()

    inference_dir = Path(args.inference_dir)
    ground_truth_dir = Path(args.ground_truth_dir)
    line_dir = Path(args.line_dir)

    results = []
    for gt in ground_truth_dir.glob("*.arrow"):
        # 记得改这里后缀
        infer = inference_dir / gt.name.replace("_gt.arrow", "_yolo11.arrow")
        line = line_dir / gt.name.replace("_gt.arrow", "_yolo11.arrow")
        result = evaluate_detection(infer, gt, args.max_distance, line)
        del result["matched_distances"]
        results.append(result)

    collect_reults(results, inference_dir.parent / "results.csv")


if __name__ == "__main__":
    # main()
    main_batch()
