from pathlib import Path
import rasterio
import geopandas as gpd
from tqdm import tqdm

# 将我的结果裁切到测试区面积上


def cut_shp_to_file(input_shape_dir: Path, image: Path, output_dir: Path):
    # 061301_spectral_center_resnet50.parquet
    # tod_P061301_2.tif

    filename = image.stem
    input_shape_path = input_shape_dir / f"{filename[5:11]}_spectral_center_resnet50.parquet"

    with rasterio.open(image) as src:
        bounds = src.bounds
        left, bottom, right, top = bounds.left, bounds.bottom, bounds.right, bounds.top
        crs = src.crs

    gdf = gpd.read_file(input_shape_path).to_crs(crs)
    gdf = gdf.cx[left:right, bottom:top]
    gdf.to_crs(32647).to_file(output_dir / f"{filename}.arrow",driver="Arrow")


if __name__ == "__main__":
    input_shape_dir = Path(r"E:\tobacco\GraduationProject\DATA\Part-2-ResNet\5_results_2025_classification")
    image_dir = Path(r"E:\tobacco\tobacco_det_data\batch2_384\images")
    output_dir = Path(r"E:\tobacco\tobacco_det_data\batch2_384\inferences_mine")

    for image in tqdm(list(image_dir.glob("*.tif"))):
        cut_shp_to_file(input_shape_dir, image, output_dir)
